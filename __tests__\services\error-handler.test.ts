import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { ReportErrorHandler, ErrorCategory, ErrorSeverity } from '@/lib/services/error-handler'
import { prisma } from '@/lib/db'

// Mock dependencies
jest.mock('@/lib/db')

const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('ReportErrorHandler', () => {
  let errorHandler: ReportErrorHandler

  beforeEach(() => {
    jest.clearAllMocks()
    errorHandler = ReportErrorHandler.getInstance()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('handleError', () => {
    it('should handle string error successfully', async () => {
      const mockErrorLog = {
        id: 'error-123',
        code: 'VAL_abc123',
        message: 'Test error message',
        category: ErrorCategory.VALIDATION,
        severity: ErrorSeverity.MEDIUM,
        context: JSON.stringify({
          userId: 'user-123',
          operation: 'test-operation',
          timestamp: expect.any(Date)
        }),
        resolved: false,
        occurrenceCount: 1,
        firstOccurrence: expect.any(Date),
        lastOccurrence: expect.any(Date)
      }

      mockPrisma.errorLog.findFirst.mockResolvedValue(null) // No similar error
      mockPrisma.errorLog.create.mockResolvedValue(mockErrorLog as any)

      const errorId = await errorHandler.handleError(
        'Test error message',
        ErrorCategory.VALIDATION,
        ErrorSeverity.MEDIUM,
        {
          userId: 'user-123',
          operation: 'test-operation'
        }
      )

      expect(errorId).toMatch(/^err_\d+_[a-z0-9]+$/)
      expect(mockPrisma.errorLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          message: 'Test error message',
          category: ErrorCategory.VALIDATION,
          severity: ErrorSeverity.MEDIUM,
          resolved: false,
          occurrenceCount: 1
        })
      })
    })

    it('should handle Error object successfully', async () => {
      const testError = new Error('Test error object')
      testError.stack = 'Error: Test error object\n    at test.js:1:1'

      mockPrisma.errorLog.findFirst.mockResolvedValue(null)
      mockPrisma.errorLog.create.mockResolvedValue({} as any)

      const errorId = await errorHandler.handleError(
        testError,
        ErrorCategory.DATABASE,
        ErrorSeverity.HIGH,
        {
          userId: 'user-123',
          operation: 'database-query'
        }
      )

      expect(errorId).toBeDefined()
      expect(mockPrisma.errorLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          message: 'Test error object',
          category: ErrorCategory.DATABASE,
          severity: ErrorSeverity.HIGH,
          context: expect.stringContaining('"stackTrace"')
        })
      })
    })

    it('should increment count for similar errors', async () => {
      const existingError = {
        id: 'existing-error-123',
        code: 'VAL_abc123',
        category: ErrorCategory.VALIDATION,
        lastOccurrence: new Date(Date.now() - 30 * 60 * 1000) // 30 minutes ago
      }

      mockPrisma.errorLog.findFirst.mockResolvedValue(existingError as any)
      mockPrisma.errorLog.update.mockResolvedValue({} as any)

      const errorId = await errorHandler.handleError(
        'Test error message',
        ErrorCategory.VALIDATION,
        ErrorSeverity.MEDIUM
      )

      expect(errorId).toBe('existing-error-123')
      expect(mockPrisma.errorLog.update).toHaveBeenCalledWith({
        where: { id: 'existing-error-123' },
        data: {
          occurrenceCount: { increment: 1 },
          lastOccurrence: expect.any(Date)
        }
      })
    })

    it('should sanitize sensitive data', async () => {
      mockPrisma.errorLog.findFirst.mockResolvedValue(null)
      mockPrisma.errorLog.create.mockResolvedValue({} as any)

      await errorHandler.handleError(
        'Test error',
        ErrorCategory.AUTHENTICATION,
        ErrorSeverity.HIGH,
        {
          userId: 'user-123',
          additionalData: {
            password: 'secret123',
            token: 'bearer-token',
            normalField: 'normal-value'
          }
        },
        { sanitizeData: true }
      )

      expect(mockPrisma.errorLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          context: expect.stringContaining('[REDACTED]')
        })
      })

      const contextString = mockPrisma.errorLog.create.mock.calls[0][0].data.context
      const context = JSON.parse(contextString)
      expect(context.additionalData.password).toBe('[REDACTED]')
      expect(context.additionalData.token).toBe('[REDACTED]')
      expect(context.additionalData.normalField).toBe('normal-value')
    })
  })

  describe('getError', () => {
    it('should retrieve error by ID', async () => {
      const mockError = {
        id: 'error-123',
        code: 'VAL_abc123',
        message: 'Test error',
        category: ErrorCategory.VALIDATION,
        severity: ErrorSeverity.MEDIUM,
        context: JSON.stringify({
          userId: 'user-123',
          timestamp: new Date()
        }),
        resolved: false,
        resolvedAt: null,
        resolvedBy: null,
        resolution: null,
        occurrenceCount: 1,
        firstOccurrence: new Date(),
        lastOccurrence: new Date()
      }

      mockPrisma.errorLog.findUnique.mockResolvedValue(mockError as any)

      const result = await errorHandler.getError('error-123')

      expect(result).toMatchObject({
        id: 'error-123',
        code: 'VAL_abc123',
        message: 'Test error',
        category: ErrorCategory.VALIDATION,
        severity: ErrorSeverity.MEDIUM,
        resolved: false,
        occurrenceCount: 1
      })
      expect(result?.context).toMatchObject({
        userId: 'user-123'
      })
    })

    it('should return null for non-existent error', async () => {
      mockPrisma.errorLog.findUnique.mockResolvedValue(null)

      const result = await errorHandler.getError('non-existent')

      expect(result).toBeNull()
    })
  })

  describe('listErrors', () => {
    it('should list errors with filters', async () => {
      const mockErrors = [
        {
          id: 'error-1',
          code: 'VAL_001',
          message: 'Validation error 1',
          category: ErrorCategory.VALIDATION,
          severity: ErrorSeverity.MEDIUM,
          context: JSON.stringify({ userId: 'user-1' }),
          resolved: false,
          resolvedAt: null,
          resolvedBy: null,
          resolution: null,
          occurrenceCount: 1,
          firstOccurrence: new Date(),
          lastOccurrence: new Date()
        },
        {
          id: 'error-2',
          code: 'DB_002',
          message: 'Database error 1',
          category: ErrorCategory.DATABASE,
          severity: ErrorSeverity.HIGH,
          context: JSON.stringify({ userId: 'user-2' }),
          resolved: true,
          resolvedAt: new Date(),
          resolvedBy: 'admin-1',
          resolution: 'Fixed database connection',
          occurrenceCount: 3,
          firstOccurrence: new Date(),
          lastOccurrence: new Date()
        }
      ]

      mockPrisma.errorLog.findMany.mockResolvedValue(mockErrors as any)
      mockPrisma.errorLog.count.mockResolvedValue(2)

      const result = await errorHandler.listErrors({
        category: ErrorCategory.VALIDATION,
        severity: ErrorSeverity.MEDIUM,
        resolved: false,
        limit: 10,
        offset: 0
      })

      expect(result.errors).toHaveLength(2)
      expect(result.total).toBe(2)
      expect(result.errors[0]).toMatchObject({
        id: 'error-1',
        category: ErrorCategory.VALIDATION,
        severity: ErrorSeverity.MEDIUM,
        resolved: false
      })
    })
  })

  describe('resolveError', () => {
    it('should resolve error successfully', async () => {
      mockPrisma.errorLog.update.mockResolvedValue({} as any)

      const result = await errorHandler.resolveError(
        'error-123',
        'Fixed validation logic',
        'admin-123'
      )

      expect(result).toBe(true)
      expect(mockPrisma.errorLog.update).toHaveBeenCalledWith({
        where: { id: 'error-123' },
        data: {
          resolved: true,
          resolvedAt: expect.any(Date),
          resolvedBy: 'admin-123',
          resolution: 'Fixed validation logic'
        }
      })
    })

    it('should handle resolution failure', async () => {
      mockPrisma.errorLog.update.mockRejectedValue(new Error('Database error'))

      const result = await errorHandler.resolveError(
        'error-123',
        'Fix attempt',
        'admin-123'
      )

      expect(result).toBe(false)
    })
  })

  describe('getErrorStatistics', () => {
    it('should calculate error statistics correctly', async () => {
      const mockErrors = [
        {
          category: ErrorCategory.VALIDATION,
          severity: ErrorSeverity.MEDIUM,
          code: 'VAL_001',
          message: 'Validation error',
          occurrenceCount: 5,
          resolved: true,
          firstOccurrence: new Date()
        },
        {
          category: ErrorCategory.DATABASE,
          severity: ErrorSeverity.HIGH,
          code: 'DB_001',
          message: 'Database error',
          occurrenceCount: 2,
          resolved: false,
          firstOccurrence: new Date()
        },
        {
          category: ErrorCategory.VALIDATION,
          severity: ErrorSeverity.LOW,
          code: 'VAL_002',
          message: 'Another validation error',
          occurrenceCount: 1,
          resolved: true,
          firstOccurrence: new Date()
        }
      ]

      mockPrisma.errorLog.findMany.mockResolvedValue(mockErrors as any)

      const statistics = await errorHandler.getErrorStatistics('day')

      expect(statistics.totalErrors).toBe(8) // Sum of occurrence counts
      expect(statistics.errorsByCategory[ErrorCategory.VALIDATION]).toBe(6)
      expect(statistics.errorsByCategory[ErrorCategory.DATABASE]).toBe(2)
      expect(statistics.errorsBySeverity[ErrorSeverity.MEDIUM]).toBe(5)
      expect(statistics.errorsBySeverity[ErrorSeverity.HIGH]).toBe(2)
      expect(statistics.errorsBySeverity[ErrorSeverity.LOW]).toBe(1)
      expect(statistics.resolutionRate).toBe(66.67) // 2 out of 3 errors resolved
      expect(statistics.topErrors).toHaveLength(3)
      expect(statistics.topErrors[0]).toMatchObject({
        code: 'VAL_001',
        count: 5
      })
    })
  })
})
