/**
 * Global Test Setup
 * 
 * This file runs once before all tests to set up the testing environment
 */

import { execSync } from 'child_process'
import fs from 'fs'
import path from 'path'

export default async function globalSetup() {
  console.log('🚀 Setting up test environment...')

  try {
    // Create test storage directory
    const testStorageDir = path.join(process.cwd(), 'test-storage')
    if (!fs.existsSync(testStorageDir)) {
      fs.mkdirSync(testStorageDir, { recursive: true })
      console.log('✓ Created test storage directory')
    }

    // Create subdirectories for different file types
    const subdirs = ['reports', 'templates', 'cache', 'temp']
    for (const subdir of subdirs) {
      const subdirPath = path.join(testStorageDir, subdir)
      if (!fs.existsSync(subdirPath)) {
        fs.mkdirSync(subdirPath, { recursive: true })
      }
    }

    // Set up test database (if using a separate test database)
    if (process.env.DATABASE_URL?.includes('test')) {
      try {
        console.log('🗄️  Setting up test database...')
        
        // Run database migrations for test environment
        execSync('npx prisma migrate deploy', {
          stdio: 'inherit',
          env: { ...process.env, DATABASE_URL: process.env.DATABASE_URL }
        })
        
        // Generate Prisma client for test environment
        execSync('npx prisma generate', {
          stdio: 'inherit'
        })
        
        console.log('✓ Test database setup complete')
      } catch (error) {
        console.warn('⚠️  Database setup failed (this may be expected in some environments)')
        console.warn(error)
      }
    }

    // Create test configuration files
    const testConfigDir = path.join(process.cwd(), 'test-config')
    if (!fs.existsSync(testConfigDir)) {
      fs.mkdirSync(testConfigDir, { recursive: true })
    }

    // Create test environment file
    const testEnvContent = `
# Test Environment Configuration
NODE_ENV=test
DATABASE_URL=postgresql://test:test@localhost:5432/qrsams_test
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=testpass
REPORTS_STORAGE_DIR=./test-storage
DIGITAL_SIGNATURE_ENABLED=true
SCHEDULING_ENABLED=false
CACHE_ENABLED=true
ENABLE_RATE_LIMITING=false
ENABLE_AUDIT_LOGGING=true
MAX_FILE_SIZE=52428800
RETENTION_DAYS=7
`

    fs.writeFileSync(path.join(testConfigDir, '.env.test'), testEnvContent.trim())

    // Create mock data files
    const mockDataDir = path.join(testConfigDir, 'mock-data')
    if (!fs.existsSync(mockDataDir)) {
      fs.mkdirSync(mockDataDir, { recursive: true })
    }

    // Sample students data
    const mockStudents = [
      {
        id: 'student-1',
        studentNumber: '123456789012',
        firstName: 'Juan',
        lastName: 'Dela Cruz',
        middleName: 'Santos',
        gradeLevel: 'Grade 7',
        section: 'A',
        guardianName: 'Pedro Dela Cruz',
        guardianContact: '+639123456789',
        address: 'Tanauan City, Batangas',
        dateEnrolled: '2024-06-01T00:00:00Z',
        status: 'ACTIVE'
      },
      {
        id: 'student-2',
        studentNumber: '123456789013',
        firstName: 'Maria',
        lastName: 'Santos',
        middleName: 'Cruz',
        gradeLevel: 'Grade 7',
        section: 'A',
        guardianName: 'Rosa Santos',
        guardianContact: '+639123456790',
        address: 'Tanauan City, Batangas',
        dateEnrolled: '2024-06-01T00:00:00Z',
        status: 'ACTIVE'
      }
    ]

    fs.writeFileSync(
      path.join(mockDataDir, 'students.json'),
      JSON.stringify(mockStudents, null, 2)
    )

    // Sample teachers data
    const mockTeachers = [
      {
        id: 'teacher-1',
        employeeNumber: 'T001',
        firstName: 'Maria',
        lastName: 'Garcia',
        email: '<EMAIL>',
        phone: '+639123456789',
        status: 'ACTIVE'
      },
      {
        id: 'teacher-2',
        employeeNumber: 'T002',
        firstName: 'Jose',
        lastName: 'Rizal',
        email: '<EMAIL>',
        phone: '+639123456790',
        status: 'ACTIVE'
      }
    ]

    fs.writeFileSync(
      path.join(mockDataDir, 'teachers.json'),
      JSON.stringify(mockTeachers, null, 2)
    )

    // Sample attendance data
    const mockAttendance = [
      {
        id: 'attendance-1',
        studentId: 'student-1',
        teacherId: 'teacher-1',
        date: '2024-01-15T00:00:00Z',
        timeIn: '2024-01-15T08:00:00Z',
        timeOut: '2024-01-15T16:00:00Z',
        status: 'PRESENT',
        remarks: null
      },
      {
        id: 'attendance-2',
        studentId: 'student-2',
        teacherId: 'teacher-1',
        date: '2024-01-15T00:00:00Z',
        timeIn: null,
        timeOut: null,
        status: 'ABSENT',
        remarks: 'Sick'
      }
    ]

    fs.writeFileSync(
      path.join(mockDataDir, 'attendance.json'),
      JSON.stringify(mockAttendance, null, 2)
    )

    // Set global test variables
    global.__TEST_STORAGE_DIR__ = testStorageDir
    global.__TEST_CONFIG_DIR__ = testConfigDir
    global.__TEST_MOCK_DATA_DIR__ = mockDataDir

    console.log('✅ Test environment setup complete')

  } catch (error) {
    console.error('❌ Test environment setup failed:', error)
    throw error
  }
}

// Declare global variables for TypeScript
declare global {
  var __TEST_STORAGE_DIR__: string
  var __TEST_CONFIG_DIR__: string
  var __TEST_MOCK_DATA_DIR__: string
}
