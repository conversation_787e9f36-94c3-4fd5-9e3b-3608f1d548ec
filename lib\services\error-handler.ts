import { prisma } from '@/lib/db'
import { reportsConfig } from '@/lib/config/reports'

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum ErrorCategory {
  VALIDATION = 'VALIDATION',
  DATABASE = 'DATABASE',
  FILE_STORAGE = 'FILE_STORAGE',
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  COMPLIANCE = 'COMPLIANCE',
  GENERATION = 'GENERATION',
  TEMPLATE = 'TEMPLATE',
  SIGNATURE = 'SIGNATURE',
  SCHEDULING = 'SCHEDULING',
  PERFORMANCE = 'PERFORMANCE',
  UNKNOWN = 'UNKNOWN'
}

export interface ErrorContext {
  userId?: string
  reportId?: string
  templateId?: string
  scheduleId?: string
  operation?: string
  requestId?: string
  userAgent?: string
  ipAddress?: string
  timestamp: Date
  stackTrace?: string
  additionalData?: Record<string, any>
}

export interface ReportError {
  id: string
  code: string
  message: string
  category: ErrorCategory
  severity: ErrorSeverity
  context: ErrorContext
  resolved: boolean
  resolvedAt?: Date
  resolvedBy?: string
  resolution?: string
  occurrenceCount: number
  firstOccurrence: Date
  lastOccurrence: Date
}

export interface ErrorHandlingOptions {
  logToDatabase: boolean
  logToConsole: boolean
  notifyAdmins: boolean
  includeStackTrace: boolean
  sanitizeData: boolean
}

export class ReportErrorHandler {
  private static instance: ReportErrorHandler
  private errorCounts: Map<string, number> = new Map()
  private recentErrors: ReportError[] = []

  private constructor() {
    // Start error cleanup interval
    setInterval(() => {
      this.cleanupOldErrors()
    }, 60 * 60 * 1000) // Every hour
  }

  static getInstance(): ReportErrorHandler {
    if (!ReportErrorHandler.instance) {
      ReportErrorHandler.instance = new ReportErrorHandler()
    }
    return ReportErrorHandler.instance
  }

  /**
   * Handle and log an error
   */
  async handleError(
    error: Error | string,
    category: ErrorCategory = ErrorCategory.UNKNOWN,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context: Partial<ErrorContext> = {},
    options: Partial<ErrorHandlingOptions> = {}
  ): Promise<string> {
    const errorId = this.generateErrorId()
    const timestamp = new Date()
    
    // Normalize error
    const normalizedError = this.normalizeError(error)
    
    // Build full context
    const fullContext: ErrorContext = {
      timestamp,
      stackTrace: normalizedError.stack,
      ...context
    }

    // Sanitize sensitive data if needed
    if (options.sanitizeData !== false) {
      this.sanitizeContext(fullContext)
    }

    // Create error record
    const reportError: ReportError = {
      id: errorId,
      code: this.generateErrorCode(category, normalizedError.message),
      message: normalizedError.message,
      category,
      severity,
      context: fullContext,
      resolved: false,
      occurrenceCount: 1,
      firstOccurrence: timestamp,
      lastOccurrence: timestamp
    }

    // Check for duplicate errors
    const existingError = await this.findSimilarError(reportError)
    if (existingError) {
      await this.incrementErrorCount(existingError.id)
      return existingError.id
    }

    // Log to console if enabled
    if (options.logToConsole !== false) {
      this.logToConsole(reportError)
    }

    // Log to database if enabled
    if (options.logToDatabase !== false) {
      await this.logToDatabase(reportError)
    }

    // Store in memory for quick access
    this.recentErrors.unshift(reportError)
    if (this.recentErrors.length > 100) {
      this.recentErrors = this.recentErrors.slice(0, 50)
    }

    // Notify admins for critical errors
    if (severity === ErrorSeverity.CRITICAL && options.notifyAdmins !== false) {
      await this.notifyAdmins(reportError)
    }

    // Update error statistics
    this.updateErrorStatistics(reportError)

    return errorId
  }

  /**
   * Get error by ID
   */
  async getError(errorId: string): Promise<ReportError | null> {
    try {
      // Check memory first
      const memoryError = this.recentErrors.find(e => e.id === errorId)
      if (memoryError) {
        return memoryError
      }

      // Check database
      const dbError = await prisma.errorLog.findUnique({
        where: { id: errorId }
      })

      if (!dbError) return null

      return {
        id: dbError.id,
        code: dbError.code,
        message: dbError.message,
        category: dbError.category as ErrorCategory,
        severity: dbError.severity as ErrorSeverity,
        context: JSON.parse(dbError.context),
        resolved: dbError.resolved,
        resolvedAt: dbError.resolvedAt,
        resolvedBy: dbError.resolvedBy,
        resolution: dbError.resolution,
        occurrenceCount: dbError.occurrenceCount,
        firstOccurrence: dbError.firstOccurrence,
        lastOccurrence: dbError.lastOccurrence
      }
    } catch (error) {
      console.error('Failed to get error:', error)
      return null
    }
  }

  /**
   * List errors with filtering
   */
  async listErrors(filters: {
    category?: ErrorCategory
    severity?: ErrorSeverity
    resolved?: boolean
    userId?: string
    dateFrom?: Date
    dateTo?: Date
    limit?: number
    offset?: number
  } = {}): Promise<{ errors: ReportError[]; total: number }> {
    try {
      const where: any = {}

      if (filters.category) where.category = filters.category
      if (filters.severity) where.severity = filters.severity
      if (filters.resolved !== undefined) where.resolved = filters.resolved
      if (filters.dateFrom) where.firstOccurrence = { gte: filters.dateFrom }
      if (filters.dateTo) {
        where.firstOccurrence = {
          ...where.firstOccurrence,
          lte: filters.dateTo
        }
      }

      // Filter by userId in context
      if (filters.userId) {
        where.context = {
          path: ['userId'],
          equals: filters.userId
        }
      }

      const [errors, total] = await Promise.all([
        prisma.errorLog.findMany({
          where,
          orderBy: { lastOccurrence: 'desc' },
          take: filters.limit || 50,
          skip: filters.offset || 0
        }),
        prisma.errorLog.count({ where })
      ])

      return {
        errors: errors.map(error => ({
          id: error.id,
          code: error.code,
          message: error.message,
          category: error.category as ErrorCategory,
          severity: error.severity as ErrorSeverity,
          context: JSON.parse(error.context),
          resolved: error.resolved,
          resolvedAt: error.resolvedAt,
          resolvedBy: error.resolvedBy,
          resolution: error.resolution,
          occurrenceCount: error.occurrenceCount,
          firstOccurrence: error.firstOccurrence,
          lastOccurrence: error.lastOccurrence
        })),
        total
      }
    } catch (error) {
      console.error('Failed to list errors:', error)
      return { errors: [], total: 0 }
    }
  }

  /**
   * Resolve an error
   */
  async resolveError(
    errorId: string,
    resolution: string,
    resolvedBy: string
  ): Promise<boolean> {
    try {
      await prisma.errorLog.update({
        where: { id: errorId },
        data: {
          resolved: true,
          resolvedAt: new Date(),
          resolvedBy,
          resolution
        }
      })

      // Update memory cache
      const memoryError = this.recentErrors.find(e => e.id === errorId)
      if (memoryError) {
        memoryError.resolved = true
        memoryError.resolvedAt = new Date()
        memoryError.resolvedBy = resolvedBy
        memoryError.resolution = resolution
      }

      return true
    } catch (error) {
      console.error('Failed to resolve error:', error)
      return false
    }
  }

  /**
   * Get error statistics
   */
  async getErrorStatistics(timeframe: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<{
    totalErrors: number
    errorsByCategory: Record<ErrorCategory, number>
    errorsBySeverity: Record<ErrorSeverity, number>
    topErrors: Array<{ code: string; count: number; message: string }>
    errorTrend: Array<{ date: Date; count: number }>
    resolutionRate: number
  }> {
    try {
      const timeframeDuration = this.getTimeframeDuration(timeframe)
      const startDate = new Date(Date.now() - timeframeDuration)

      const errors = await prisma.errorLog.findMany({
        where: {
          firstOccurrence: { gte: startDate }
        },
        select: {
          category: true,
          severity: true,
          code: true,
          message: true,
          occurrenceCount: true,
          resolved: true,
          firstOccurrence: true
        }
      })

      const totalErrors = errors.reduce((sum, error) => sum + error.occurrenceCount, 0)
      const resolvedErrors = errors.filter(e => e.resolved).length

      // Group by category
      const errorsByCategory = errors.reduce((acc, error) => {
        const category = error.category as ErrorCategory
        acc[category] = (acc[category] || 0) + error.occurrenceCount
        return acc
      }, {} as Record<ErrorCategory, number>)

      // Group by severity
      const errorsBySeverity = errors.reduce((acc, error) => {
        const severity = error.severity as ErrorSeverity
        acc[severity] = (acc[severity] || 0) + error.occurrenceCount
        return acc
      }, {} as Record<ErrorSeverity, number>)

      // Top errors
      const topErrors = errors
        .sort((a, b) => b.occurrenceCount - a.occurrenceCount)
        .slice(0, 10)
        .map(error => ({
          code: error.code,
          count: error.occurrenceCount,
          message: error.message
        }))

      // Error trend (simplified)
      const errorTrend = this.calculateErrorTrend(errors, timeframe)

      return {
        totalErrors,
        errorsByCategory,
        errorsBySeverity,
        topErrors,
        errorTrend,
        resolutionRate: errors.length > 0 ? (resolvedErrors / errors.length) * 100 : 0
      }
    } catch (error) {
      console.error('Failed to get error statistics:', error)
      return {
        totalErrors: 0,
        errorsByCategory: {} as Record<ErrorCategory, number>,
        errorsBySeverity: {} as Record<ErrorSeverity, number>,
        topErrors: [],
        errorTrend: [],
        resolutionRate: 0
      }
    }
  }

  // Private helper methods
  private normalizeError(error: Error | string): Error {
    if (typeof error === 'string') {
      return new Error(error)
    }
    return error
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateErrorCode(category: ErrorCategory, message: string): string {
    const categoryCode = category.substring(0, 3).toUpperCase()
    const messageHash = this.hashString(message).substring(0, 6)
    return `${categoryCode}_${messageHash}`
  }

  private hashString(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36)
  }

  private sanitizeContext(context: ErrorContext): void {
    // Remove sensitive information
    if (context.additionalData) {
      const sensitiveKeys = ['password', 'token', 'secret', 'key', 'auth']
      for (const key of Object.keys(context.additionalData)) {
        if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
          context.additionalData[key] = '[REDACTED]'
        }
      }
    }

    // Truncate long stack traces
    if (context.stackTrace && context.stackTrace.length > 2000) {
      context.stackTrace = context.stackTrace.substring(0, 2000) + '...[TRUNCATED]'
    }
  }

  private async findSimilarError(error: ReportError): Promise<ReportError | null> {
    try {
      const recentError = await prisma.errorLog.findFirst({
        where: {
          code: error.code,
          category: error.category,
          lastOccurrence: {
            gte: new Date(Date.now() - 60 * 60 * 1000) // Within last hour
          }
        }
      })

      if (!recentError) return null

      return {
        id: recentError.id,
        code: recentError.code,
        message: recentError.message,
        category: recentError.category as ErrorCategory,
        severity: recentError.severity as ErrorSeverity,
        context: JSON.parse(recentError.context),
        resolved: recentError.resolved,
        resolvedAt: recentError.resolvedAt,
        resolvedBy: recentError.resolvedBy,
        resolution: recentError.resolution,
        occurrenceCount: recentError.occurrenceCount,
        firstOccurrence: recentError.firstOccurrence,
        lastOccurrence: recentError.lastOccurrence
      }
    } catch (error) {
      return null
    }
  }

  private async incrementErrorCount(errorId: string): Promise<void> {
    try {
      await prisma.errorLog.update({
        where: { id: errorId },
        data: {
          occurrenceCount: { increment: 1 },
          lastOccurrence: new Date()
        }
      })
    } catch (error) {
      console.error('Failed to increment error count:', error)
    }
  }

  private logToConsole(error: ReportError): void {
    const logLevel = this.getLogLevel(error.severity)
    const message = `[${error.category}] ${error.message}`
    const context = `Context: ${JSON.stringify(error.context, null, 2)}`

    switch (logLevel) {
      case 'error':
        console.error(message, context)
        break
      case 'warn':
        console.warn(message, context)
        break
      default:
        console.log(message, context)
    }
  }

  private async logToDatabase(error: ReportError): Promise<void> {
    try {
      await prisma.errorLog.create({
        data: {
          id: error.id,
          code: error.code,
          message: error.message,
          category: error.category,
          severity: error.severity,
          context: JSON.stringify(error.context),
          resolved: error.resolved,
          occurrenceCount: error.occurrenceCount,
          firstOccurrence: error.firstOccurrence,
          lastOccurrence: error.lastOccurrence
        }
      })
    } catch (dbError) {
      console.error('Failed to log error to database:', dbError)
    }
  }

  private async notifyAdmins(error: ReportError): Promise<void> {
    try {
      // This would integrate with your notification system
      console.error(`CRITICAL ERROR: ${error.message}`, error.context)
      
      // In a real implementation, you might:
      // - Send email notifications
      // - Create Slack/Teams alerts
      // - Trigger monitoring system alerts
    } catch (error) {
      console.error('Failed to notify admins:', error)
    }
  }

  private updateErrorStatistics(error: ReportError): void {
    const key = `${error.category}_${error.severity}`
    this.errorCounts.set(key, (this.errorCounts.get(key) || 0) + 1)
  }

  private getLogLevel(severity: ErrorSeverity): 'error' | 'warn' | 'log' {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error'
      case ErrorSeverity.MEDIUM:
        return 'warn'
      default:
        return 'log'
    }
  }

  private getTimeframeDuration(timeframe: string): number {
    switch (timeframe) {
      case 'hour': return 60 * 60 * 1000
      case 'day': return 24 * 60 * 60 * 1000
      case 'week': return 7 * 24 * 60 * 60 * 1000
      case 'month': return 30 * 24 * 60 * 60 * 1000
      default: return 24 * 60 * 60 * 1000
    }
  }

  private calculateErrorTrend(errors: any[], timeframe: string): Array<{ date: Date; count: number }> {
    // Simplified trend calculation
    const now = new Date()
    const trend: Array<{ date: Date; count: number }> = []
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now.getTime() - i * this.getTimeframeDuration(timeframe) / 7)
      const count = errors.filter(e => 
        new Date(e.firstOccurrence) >= date && 
        new Date(e.firstOccurrence) < new Date(date.getTime() + this.getTimeframeDuration(timeframe) / 7)
      ).length
      
      trend.push({ date, count })
    }
    
    return trend
  }

  private cleanupOldErrors(): void {
    // Clean up old errors from memory
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000) // 24 hours
    this.recentErrors = this.recentErrors.filter(error => 
      error.lastOccurrence.getTime() > cutoffTime
    )

    // Clean up error counts
    this.errorCounts.clear()
  }
}

// Error handling decorator
export function handleErrors(
  category: ErrorCategory = ErrorCategory.UNKNOWN,
  severity: ErrorSeverity = ErrorSeverity.MEDIUM
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const errorHandler = ReportErrorHandler.getInstance()
      
      try {
        return await method.apply(this, args)
      } catch (error) {
        const errorId = await errorHandler.handleError(
          error as Error,
          category,
          severity,
          {
            operation: `${target.constructor.name}.${propertyName}`,
            additionalData: { args: args.length }
          }
        )
        
        // Re-throw with error ID for upstream handling
        const enhancedError = error as any
        enhancedError.errorId = errorId
        throw enhancedError
      }
    }

    return descriptor
  }
}
