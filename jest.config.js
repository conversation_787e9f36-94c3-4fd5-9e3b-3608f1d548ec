/** @type {import('jest').Config} */
const config = {
  // Test environment
  testEnvironment: 'node',

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/__tests__/setup.ts'],

  // Test file patterns
  testMatch: [
    '<rootDir>/__tests__/**/*.test.{js,ts}',
    '<rootDir>/**/__tests__/**/*.{js,ts}',
    '<rootDir>/**/*.{test,spec}.{js,ts}'
  ],

  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  collectCoverageFrom: [
    'lib/**/*.{js,ts}',
    'app/api/**/*.{js,ts}',
    '!lib/**/*.d.ts',
    '!lib/db.ts', // Exclude database connection
    '!**/*.test.{js,ts}',
    '!**/__tests__/**',
    '!**/node_modules/**',
    '!coverage/**'
  ],

  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    },
    // Specific thresholds for critical services
    'lib/services/report-service.ts': {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    'lib/services/sf2-generator.ts': {
      branches: 75,
      functions: 75,
      lines: 75,
      statements: 75
    },
    'lib/services/sf4-generator.ts': {
      branches: 75,
      functions: 75,
      lines: 75,
      statements: 75
    },
    'lib/services/digital-signature.ts': {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    'lib/services/compliance-validator.ts': {
      branches: 75,
      functions: 75,
      lines: 75,
      statements: 75
    }
  },

  // Module name mapping for path aliases
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1',
    '^@/lib/(.*)$': '<rootDir>/lib/$1',
    '^@/app/(.*)$': '<rootDir>/app/$1',
    '^@/types/(.*)$': '<rootDir>/types/$1'
  },

  // Transform configuration
  preset: 'ts-jest',
  extensionsToTreatAsEsm: ['.ts'],
  globals: {
    'ts-jest': {
      useESM: true,
      tsconfig: {
        module: 'esnext',
        target: 'es2020'
      }
    }
  },

  // Module file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],

  // Transform patterns
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
    '^.+\\.(js|jsx)$': 'babel-jest'
  },

  // Ignore patterns
  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/node_modules/',
    '<rootDir>/coverage/',
    '<rootDir>/dist/'
  ],

  // Module paths to ignore during transformation
  transformIgnorePatterns: [
    'node_modules/(?!(.*\\.mjs$))'
  ],

  // Clear mocks between tests
  clearMocks: true,
  restoreMocks: true,

  // Verbose output
  verbose: true,

  // Test timeout
  testTimeout: 30000,

  // Error handling
  errorOnDeprecated: true,

  // Watch mode configuration
  watchman: true,
  watchPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/.next/',
    '<rootDir>/coverage/',
    '<rootDir>/dist/'
  ],

  // Reporters
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputDirectory: 'coverage',
        outputName: 'junit.xml',
        classNameTemplate: '{classname}',
        titleTemplate: '{title}',
        ancestorSeparator: ' › ',
        usePathForSuiteName: true
      }
    ]
  ],

  // Global setup and teardown
  globalSetup: '<rootDir>/__tests__/global-setup.ts',
  globalTeardown: '<rootDir>/__tests__/global-teardown.ts',

  // Test result processor
  testResultsProcessor: 'jest-sonar-reporter',

  // Snapshot configuration
  snapshotSerializers: ['jest-serializer-path'],

  // Mock configuration
  automock: false,
  unmockedModulePathPatterns: [
    'node_modules/react',
    'node_modules/react-dom'
  ]
}

module.exports = config
