import { prisma } from '@/lib/db'
import { reportsConfig } from '@/lib/config/reports'

export enum AuditAction {
  // Report actions
  GENERATE_REPORT = 'GENERATE_REPORT',
  DOWNLOAD_REPORT = 'DOWNLOAD_REPORT',
  DELETE_REPORT = 'DELETE_REPORT',
  UPDATE_REPORT = 'UPDATE_REPORT',
  REGENERATE_REPORT = 'REGENERATE_REPORT',
  ARCHIVE_REPORT = 'ARCHIVE_REPORT',
  
  // Template actions
  CREATE_TEMPLATE = 'CREATE_TEMPLATE',
  UPDATE_TEMPLATE = 'UPDATE_TEMPLATE',
  DELETE_TEMPLATE = 'DELETE_TEMPLATE',
  
  // Schedule actions
  CREATE_SCHEDULE = 'CREATE_SCHEDULE',
  UPDATE_SCHEDULE = 'UPDATE_SCHEDULE',
  DELETE_SCHEDULE = 'DELETE_SCHEDULE',
  EXECUTE_SCHEDULE = 'EXECUTE_SCHEDULE',
  
  // Signature actions
  SIGN_REPORT = 'SIGN_REPORT',
  REVOKE_SIGNATURE = 'REVOKE_SIGNATURE',
  VALIDATE_SIGNATURE = 'VALIDATE_SIGNATURE',
  
  // System actions
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  PERMISSION_CHANGE = 'PERMISSION_CHANGE',
  SYSTEM_CONFIG_CHANGE = 'SYSTEM_CONFIG_CHANGE',
  
  // Performance actions
  PERFORMANCE_ACTION = 'PERFORMANCE_ACTION',
  CACHE_OPERATION = 'CACHE_OPERATION',
  
  // Error actions
  ERROR_OCCURRED = 'ERROR_OCCURRED',
  ERROR_RESOLVED = 'ERROR_RESOLVED'
}

export enum AuditResult {
  SUCCESS = 'SUCCESS',
  FAILURE = 'FAILURE',
  PARTIAL = 'PARTIAL',
  CANCELLED = 'CANCELLED'
}

export interface AuditLogEntry {
  id: string
  userId: string
  action: AuditAction
  resource: string
  resourceId?: string
  result: AuditResult
  timestamp: Date
  ipAddress?: string
  userAgent?: string
  sessionId?: string
  details: Record<string, any>
  duration?: number
  errorMessage?: string
}

export interface AuditSearchFilters {
  userId?: string
  action?: AuditAction
  resource?: string
  result?: AuditResult
  dateFrom?: Date
  dateTo?: Date
  ipAddress?: string
  limit?: number
  offset?: number
}

export class AuditLoggerService {
  private static instance: AuditLoggerService
  private pendingLogs: AuditLogEntry[] = []
  private batchSize: number = 50
  private flushInterval: number = 30000 // 30 seconds

  private constructor() {
    // Start batch processing
    setInterval(() => {
      this.flushPendingLogs()
    }, this.flushInterval)

    // Cleanup old logs periodically
    setInterval(() => {
      this.cleanupOldLogs()
    }, 24 * 60 * 60 * 1000) // Daily
  }

  static getInstance(): AuditLoggerService {
    if (!AuditLoggerService.instance) {
      AuditLoggerService.instance = new AuditLoggerService()
    }
    return AuditLoggerService.instance
  }

  /**
   * Log an audit event
   */
  async log(
    userId: string,
    action: AuditAction,
    resource: string,
    result: AuditResult = AuditResult.SUCCESS,
    options: {
      resourceId?: string
      ipAddress?: string
      userAgent?: string
      sessionId?: string
      details?: Record<string, any>
      duration?: number
      errorMessage?: string
      immediate?: boolean
    } = {}
  ): Promise<string> {
    const auditId = this.generateAuditId()
    
    const auditEntry: AuditLogEntry = {
      id: auditId,
      userId,
      action,
      resource,
      resourceId: options.resourceId,
      result,
      timestamp: new Date(),
      ipAddress: options.ipAddress,
      userAgent: options.userAgent,
      sessionId: options.sessionId,
      details: this.sanitizeDetails(options.details || {}),
      duration: options.duration,
      errorMessage: options.errorMessage
    }

    if (options.immediate || !reportsConfig.security.enableAuditLogging) {
      // Log immediately
      await this.writeToDatabase(auditEntry)
    } else {
      // Add to batch
      this.pendingLogs.push(auditEntry)
      
      // Flush if batch is full
      if (this.pendingLogs.length >= this.batchSize) {
        await this.flushPendingLogs()
      }
    }

    return auditId
  }

  /**
   * Log a successful action
   */
  async logSuccess(
    userId: string,
    action: AuditAction,
    resource: string,
    options: Omit<Parameters<typeof this.log>[4], 'result'> = {}
  ): Promise<string> {
    return this.log(userId, action, resource, AuditResult.SUCCESS, options)
  }

  /**
   * Log a failed action
   */
  async logFailure(
    userId: string,
    action: AuditAction,
    resource: string,
    errorMessage: string,
    options: Omit<Parameters<typeof this.log>[4], 'result' | 'errorMessage'> = {}
  ): Promise<string> {
    return this.log(userId, action, resource, AuditResult.FAILURE, {
      ...options,
      errorMessage
    })
  }

  /**
   * Search audit logs
   */
  async search(filters: AuditSearchFilters): Promise<{
    logs: AuditLogEntry[]
    total: number
  }> {
    try {
      const where: any = {}

      if (filters.userId) where.userId = filters.userId
      if (filters.action) where.action = filters.action
      if (filters.resource) where.resource = filters.resource
      if (filters.result) where.result = filters.result
      if (filters.ipAddress) where.ipAddress = filters.ipAddress

      if (filters.dateFrom || filters.dateTo) {
        where.timestamp = {}
        if (filters.dateFrom) where.timestamp.gte = filters.dateFrom
        if (filters.dateTo) where.timestamp.lte = filters.dateTo
      }

      const [logs, total] = await Promise.all([
        prisma.auditLog.findMany({
          where,
          orderBy: { timestamp: 'desc' },
          take: filters.limit || 100,
          skip: filters.offset || 0
        }),
        prisma.auditLog.count({ where })
      ])

      return {
        logs: logs.map(log => ({
          id: log.id,
          userId: log.userId,
          action: log.action as AuditAction,
          resource: log.resource,
          resourceId: log.resourceId,
          result: log.result as AuditResult,
          timestamp: log.timestamp,
          ipAddress: log.ipAddress,
          userAgent: log.userAgent,
          sessionId: log.sessionId,
          details: JSON.parse(log.details),
          duration: log.duration,
          errorMessage: log.errorMessage
        })),
        total
      }
    } catch (error) {
      console.error('Audit log search failed:', error)
      return { logs: [], total: 0 }
    }
  }

  /**
   * Get audit statistics
   */
  async getStatistics(timeframe: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<{
    totalActions: number
    actionsByType: Record<AuditAction, number>
    actionsByResult: Record<AuditResult, number>
    actionsByUser: Array<{ userId: string; count: number }>
    actionsByResource: Record<string, number>
    timeline: Array<{ date: Date; count: number }>
    topUsers: Array<{ userId: string; actions: number }>
    failureRate: number
  }> {
    try {
      const timeframeDuration = this.getTimeframeDuration(timeframe)
      const startDate = new Date(Date.now() - timeframeDuration)

      const logs = await prisma.auditLog.findMany({
        where: {
          timestamp: { gte: startDate }
        },
        select: {
          action: true,
          result: true,
          userId: true,
          resource: true,
          timestamp: true
        }
      })

      const totalActions = logs.length
      const failedActions = logs.filter(log => log.result === AuditResult.FAILURE).length

      // Group by action type
      const actionsByType = logs.reduce((acc, log) => {
        const action = log.action as AuditAction
        acc[action] = (acc[action] || 0) + 1
        return acc
      }, {} as Record<AuditAction, number>)

      // Group by result
      const actionsByResult = logs.reduce((acc, log) => {
        const result = log.result as AuditResult
        acc[result] = (acc[result] || 0) + 1
        return acc
      }, {} as Record<AuditResult, number>)

      // Group by user
      const userCounts = logs.reduce((acc, log) => {
        acc[log.userId] = (acc[log.userId] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      const actionsByUser = Object.entries(userCounts).map(([userId, count]) => ({
        userId,
        count
      }))

      // Group by resource
      const actionsByResource = logs.reduce((acc, log) => {
        acc[log.resource] = (acc[log.resource] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      // Timeline (simplified)
      const timeline = this.calculateTimeline(logs, timeframe)

      // Top users
      const topUsers = actionsByUser
        .sort((a, b) => b.count - a.count)
        .slice(0, 10)
        .map(user => ({ userId: user.userId, actions: user.count }))

      return {
        totalActions,
        actionsByType,
        actionsByResult,
        actionsByUser,
        actionsByResource,
        timeline,
        topUsers,
        failureRate: totalActions > 0 ? (failedActions / totalActions) * 100 : 0
      }
    } catch (error) {
      console.error('Failed to get audit statistics:', error)
      return {
        totalActions: 0,
        actionsByType: {} as Record<AuditAction, number>,
        actionsByResult: {} as Record<AuditResult, number>,
        actionsByUser: [],
        actionsByResource: {},
        timeline: [],
        topUsers: [],
        failureRate: 0
      }
    }
  }

  /**
   * Export audit logs
   */
  async exportLogs(
    filters: AuditSearchFilters,
    format: 'csv' | 'json' = 'csv'
  ): Promise<string> {
    try {
      const { logs } = await this.search({ ...filters, limit: 10000 })

      if (format === 'json') {
        return JSON.stringify(logs, null, 2)
      }

      // CSV format
      const headers = [
        'ID', 'User ID', 'Action', 'Resource', 'Resource ID', 'Result',
        'Timestamp', 'IP Address', 'Duration', 'Error Message'
      ]

      const csvRows = [
        headers.join(','),
        ...logs.map(log => [
          log.id,
          log.userId,
          log.action,
          log.resource,
          log.resourceId || '',
          log.result,
          log.timestamp.toISOString(),
          log.ipAddress || '',
          log.duration || '',
          log.errorMessage || ''
        ].map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
      ]

      return csvRows.join('\n')
    } catch (error) {
      console.error('Failed to export audit logs:', error)
      throw new Error('Export failed')
    }
  }

  // Private helper methods
  private generateAuditId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private sanitizeDetails(details: Record<string, any>): Record<string, any> {
    const sanitized = { ...details }
    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'auth', 'credential']

    const sanitizeObject = (obj: any): any => {
      if (typeof obj !== 'object' || obj === null) return obj

      if (Array.isArray(obj)) {
        return obj.map(sanitizeObject)
      }

      const result: any = {}
      for (const [key, value] of Object.entries(obj)) {
        if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
          result[key] = '[REDACTED]'
        } else if (typeof value === 'object') {
          result[key] = sanitizeObject(value)
        } else {
          result[key] = value
        }
      }
      return result
    }

    return sanitizeObject(sanitized)
  }

  private async writeToDatabase(entry: AuditLogEntry): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          id: entry.id,
          userId: entry.userId,
          action: entry.action,
          resource: entry.resource,
          resourceId: entry.resourceId,
          result: entry.result,
          timestamp: entry.timestamp,
          ipAddress: entry.ipAddress,
          userAgent: entry.userAgent,
          sessionId: entry.sessionId,
          details: JSON.stringify(entry.details),
          duration: entry.duration,
          errorMessage: entry.errorMessage
        }
      })
    } catch (error) {
      console.error('Failed to write audit log to database:', error)
    }
  }

  private async flushPendingLogs(): Promise<void> {
    if (this.pendingLogs.length === 0) return

    const logsToFlush = [...this.pendingLogs]
    this.pendingLogs = []

    try {
      await prisma.auditLog.createMany({
        data: logsToFlush.map(entry => ({
          id: entry.id,
          userId: entry.userId,
          action: entry.action,
          resource: entry.resource,
          resourceId: entry.resourceId,
          result: entry.result,
          timestamp: entry.timestamp,
          ipAddress: entry.ipAddress,
          userAgent: entry.userAgent,
          sessionId: entry.sessionId,
          details: JSON.stringify(entry.details),
          duration: entry.duration,
          errorMessage: entry.errorMessage
        }))
      })
    } catch (error) {
      console.error('Failed to flush audit logs:', error)
      // Re-add failed logs to pending (with limit to prevent infinite growth)
      if (this.pendingLogs.length < 1000) {
        this.pendingLogs.unshift(...logsToFlush)
      }
    }
  }

  private async cleanupOldLogs(): Promise<void> {
    try {
      const retentionDays = 90 // Keep logs for 90 days
      const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000)

      const result = await prisma.auditLog.deleteMany({
        where: {
          timestamp: { lt: cutoffDate }
        }
      })

      console.log(`Cleaned up ${result.count} old audit logs`)
    } catch (error) {
      console.error('Failed to cleanup old audit logs:', error)
    }
  }

  private getTimeframeDuration(timeframe: string): number {
    switch (timeframe) {
      case 'hour': return 60 * 60 * 1000
      case 'day': return 24 * 60 * 60 * 1000
      case 'week': return 7 * 24 * 60 * 60 * 1000
      case 'month': return 30 * 24 * 60 * 60 * 1000
      default: return 24 * 60 * 60 * 1000
    }
  }

  private calculateTimeline(logs: any[], timeframe: string): Array<{ date: Date; count: number }> {
    const now = new Date()
    const timeline: Array<{ date: Date; count: number }> = []
    const intervalDuration = this.getTimeframeDuration(timeframe) / 24 // 24 intervals

    for (let i = 23; i >= 0; i--) {
      const date = new Date(now.getTime() - i * intervalDuration)
      const count = logs.filter(log => {
        const logTime = new Date(log.timestamp).getTime()
        return logTime >= date.getTime() && logTime < date.getTime() + intervalDuration
      }).length

      timeline.push({ date, count })
    }

    return timeline
  }
}

// Audit logging decorator
export function auditLog(
  action: AuditAction,
  resource: string,
  options: {
    includeArgs?: boolean
    includeResult?: boolean
    trackDuration?: boolean
  } = {}
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const auditLogger = AuditLoggerService.getInstance()
      const startTime = Date.now()
      
      // Extract user context (this would depend on your implementation)
      const userId = (this as any).userId || 'system'
      
      try {
        const result = await method.apply(this, args)
        
        const duration = options.trackDuration ? Date.now() - startTime : undefined
        const details: any = {}
        
        if (options.includeArgs) {
          details.arguments = args
        }
        
        if (options.includeResult) {
          details.result = result
        }

        await auditLogger.logSuccess(userId, action, resource, {
          details,
          duration
        })

        return result
      } catch (error) {
        const duration = options.trackDuration ? Date.now() - startTime : undefined
        
        await auditLogger.logFailure(
          userId,
          action,
          resource,
          error instanceof Error ? error.message : 'Unknown error',
          { duration }
        )

        throw error
      }
    }

    return descriptor
  }
}
