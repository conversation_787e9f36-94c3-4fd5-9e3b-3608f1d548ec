/**
 * Jest Test Setup Configuration
 * 
 * This file configures the testing environment for the QR-SAMS Reports System
 */

import { jest } from '@jest/globals'

// Mock environment variables for testing
process.env.NODE_ENV = 'test'
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/qrsams_test'
process.env.SMTP_HOST = 'localhost'
process.env.SMTP_PORT = '587'
process.env.SMTP_USER = '<EMAIL>'
process.env.SMTP_PASS = 'testpass'
process.env.REPORTS_STORAGE_DIR = './test-storage'
process.env.DIGITAL_SIGNATURE_ENABLED = 'true'
process.env.SCHEDULING_ENABLED = 'true'
process.env.CACHE_ENABLED = 'true'

// Global test timeout
jest.setTimeout(30000)

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error
const originalConsoleWarn = console.warn
const originalConsoleLog = console.log

beforeEach(() => {
  // Reset console mocks before each test
  console.error = jest.fn()
  console.warn = jest.fn()
  console.log = jest.fn()
})

afterEach(() => {
  // Restore console methods after each test
  console.error = originalConsoleError
  console.warn = originalConsoleWarn
  console.log = originalConsoleLog
})

// Global test utilities
global.testUtils = {
  // Create mock user
  createMockUser: (overrides = {}) => ({
    id: 'test-user-123',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    role: 'TEACHER',
    status: 'ACTIVE',
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  }),

  // Create mock student
  createMockStudent: (overrides = {}) => ({
    id: 'test-student-123',
    studentNumber: '123456789012',
    firstName: 'Juan',
    lastName: 'Dela Cruz',
    middleName: 'Santos',
    gradeLevel: 'Grade 7',
    section: 'A',
    guardianName: 'Pedro Dela Cruz',
    guardianContact: '+639123456789',
    address: 'Tanauan City, Batangas',
    dateEnrolled: new Date('2024-06-01'),
    status: 'ACTIVE',
    ...overrides
  }),

  // Create mock teacher
  createMockTeacher: (overrides = {}) => ({
    id: 'test-teacher-123',
    employeeNumber: 'T001',
    firstName: 'Maria',
    lastName: 'Santos',
    email: '<EMAIL>',
    phone: '+639123456789',
    status: 'ACTIVE',
    ...overrides
  }),

  // Create mock attendance record
  createMockAttendance: (overrides = {}) => ({
    id: 'test-attendance-123',
    studentId: 'test-student-123',
    teacherId: 'test-teacher-123',
    date: new Date('2024-01-15'),
    timeIn: new Date('2024-01-15T08:00:00Z'),
    timeOut: new Date('2024-01-15T16:00:00Z'),
    status: 'PRESENT',
    remarks: null,
    ...overrides
  }),

  // Create mock report
  createMockReport: (overrides = {}) => ({
    id: 'test-report-123',
    name: 'Test Report',
    description: 'Test report description',
    type: 'SF2',
    status: 'COMPLETED',
    fileUrl: 'reports/test-report.pdf',
    fileName: 'test-report.pdf',
    fileSize: 1024000,
    generatedBy: 'test-user-123',
    generatedAt: new Date(),
    completedAt: new Date(),
    downloadCount: 0,
    isArchived: false,
    parameters: JSON.stringify({
      dateRange: {
        start: new Date('2024-01-15'),
        end: new Date('2024-01-15')
      },
      filters: {
        grades: ['Grade 7'],
        sections: ['A']
      },
      format: 'pdf'
    }),
    ...overrides
  }),

  // Create mock report template
  createMockTemplate: (overrides = {}) => ({
    id: 'test-template-123',
    name: 'Test Template',
    description: 'Test template description',
    type: 'SF2',
    isOfficial: true,
    isDefault: true,
    complianceLevel: 'FULL',
    digitalSignatureRequired: true,
    layout: JSON.stringify({
      orientation: 'portrait',
      pageSize: 'A4',
      margins: { top: 20, right: 15, bottom: 20, left: 15 }
    }),
    fields: JSON.stringify([]),
    styling: JSON.stringify({ theme: 'default' }),
    requiredFields: JSON.stringify(['schoolInfo', 'attendanceSummary']),
    optionalFields: JSON.stringify(['weatherCondition']),
    validationRules: JSON.stringify({}),
    version: '1.0',
    createdBy: 'system',
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  }),

  // Create mock digital signature
  createMockSignature: (overrides = {}) => ({
    id: 'test-signature-123',
    reportId: 'test-report-123',
    signerId: 'test-user-123',
    signerName: 'Test User',
    signerRole: 'teacher',
    signature: 'mock-signature-data',
    certificate: 'mock-certificate-data',
    algorithm: 'RSA-SHA256',
    timestamp: new Date(),
    location: 'School Office',
    reason: 'Report approval',
    isValid: true,
    revokedAt: null,
    revokedReason: null,
    ...overrides
  }),

  // Create mock error log
  createMockError: (overrides = {}) => ({
    id: 'test-error-123',
    code: 'TEST_001',
    message: 'Test error message',
    category: 'VALIDATION',
    severity: 'MEDIUM',
    context: JSON.stringify({
      userId: 'test-user-123',
      operation: 'test-operation',
      timestamp: new Date()
    }),
    resolved: false,
    resolvedAt: null,
    resolvedBy: null,
    resolution: null,
    occurrenceCount: 1,
    firstOccurrence: new Date(),
    lastOccurrence: new Date(),
    ...overrides
  }),

  // Create mock audit log
  createMockAuditLog: (overrides = {}) => ({
    id: 'test-audit-123',
    userId: 'test-user-123',
    action: 'GENERATE_REPORT',
    resource: 'report',
    resourceId: 'test-report-123',
    result: 'SUCCESS',
    timestamp: new Date(),
    ipAddress: '127.0.0.1',
    userAgent: 'Test User Agent',
    sessionId: 'test-session-123',
    details: JSON.stringify({
      reportType: 'SF2',
      format: 'pdf'
    }),
    duration: 5000,
    errorMessage: null,
    ...overrides
  }),

  // Wait for async operations
  waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  // Generate test date ranges
  getDateRange: (days: number = 1) => ({
    start: new Date(Date.now() - days * 24 * 60 * 60 * 1000),
    end: new Date()
  }),

  // Generate random test data
  randomString: (length: number = 10) => 
    Math.random().toString(36).substring(2, length + 2),

  randomNumber: (min: number = 1, max: number = 100) => 
    Math.floor(Math.random() * (max - min + 1)) + min,

  randomEmail: () => 
    `test${Math.random().toString(36).substring(2)}@example.com`,

  randomPhoneNumber: () => 
    `+639${Math.random().toString().substring(2, 11)}`,

  // Mock request helpers
  createMockRequest: (url: string, options: any = {}) => ({
    url,
    method: options.method || 'GET',
    headers: new Map(Object.entries(options.headers || {})),
    json: async () => options.body ? JSON.parse(options.body) : {},
    text: async () => options.body || '',
    ...options
  }),

  createMockResponse: () => ({
    status: 200,
    headers: new Map(),
    json: jest.fn(),
    text: jest.fn(),
    ok: true
  })
}

// Declare global types for TypeScript
declare global {
  var testUtils: {
    createMockUser: (overrides?: any) => any
    createMockStudent: (overrides?: any) => any
    createMockTeacher: (overrides?: any) => any
    createMockAttendance: (overrides?: any) => any
    createMockReport: (overrides?: any) => any
    createMockTemplate: (overrides?: any) => any
    createMockSignature: (overrides?: any) => any
    createMockError: (overrides?: any) => any
    createMockAuditLog: (overrides?: any) => any
    waitFor: (ms: number) => Promise<void>
    getDateRange: (days?: number) => { start: Date; end: Date }
    randomString: (length?: number) => string
    randomNumber: (min?: number, max?: number) => number
    randomEmail: () => string
    randomPhoneNumber: () => string
    createMockRequest: (url: string, options?: any) => any
    createMockResponse: () => any
  }
}

export {}
