import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { DigitalSignatureService } from '@/lib/services/digital-signature'
import { prisma } from '@/lib/db'

// Mock dependencies
jest.mock('@/lib/db')
jest.mock('crypto')

const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('DigitalSignatureService', () => {
  let digitalSignatureService: DigitalSignatureService

  beforeEach(() => {
    jest.clearAllMocks()
    digitalSignatureService = DigitalSignatureService.getInstance()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('signReport', () => {
    const mockSignatureRequest = {
      reportId: 'report-123',
      signerId: 'user-123',
      signerRole: 'teacher' as const,
      documentHash: 'abc123hash',
      timestamp: new Date('2024-01-15T10:00:00Z'),
      location: 'Principal Office',
      reason: 'Approval of daily attendance report'
    }

    const mockUser = {
      id: 'user-123',
      firstName: 'Maria',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      role: 'TEACHER',
      status: 'ACTIVE'
    }

    beforeEach(() => {
      mockPrisma.user.findUnique.mockResolvedValue(mockUser as any)
    })

    it('should create digital signature successfully', async () => {
      const mockSignature = {
        id: 'signature-123',
        reportId: mockSignatureRequest.reportId,
        signerId: mockSignatureRequest.signerId,
        signerName: 'Maria Santos',
        signerRole: mockSignatureRequest.signerRole,
        signature: 'mock-signature-data',
        certificate: 'mock-certificate-data',
        algorithm: 'RSA-SHA256',
        timestamp: mockSignatureRequest.timestamp,
        location: mockSignatureRequest.location,
        reason: mockSignatureRequest.reason,
        isValid: true
      }

      mockPrisma.digitalSignature.create.mockResolvedValue(mockSignature as any)
      mockPrisma.generatedReport.update.mockResolvedValue({} as any)

      const result = await digitalSignatureService.signReport(mockSignatureRequest)

      expect(result).toMatchObject({
        id: 'signature-123',
        reportId: mockSignatureRequest.reportId,
        signerId: mockSignatureRequest.signerId,
        signerName: 'Maria Santos',
        signerRole: mockSignatureRequest.signerRole,
        isValid: true
      })

      expect(mockPrisma.digitalSignature.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          reportId: mockSignatureRequest.reportId,
          signerId: mockSignatureRequest.signerId,
          signerRole: mockSignatureRequest.signerRole,
          isValid: true
        })
      })

      expect(mockPrisma.generatedReport.update).toHaveBeenCalledWith({
        where: { id: mockSignatureRequest.reportId },
        data: {
          digitalSignature: expect.stringContaining('signature-123')
        }
      })
    })

    it('should reject signing for non-existent user', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(null)

      await expect(digitalSignatureService.signReport(mockSignatureRequest))
        .rejects.toThrow('Signer not found')
    })

    it('should reject signing for inactive user', async () => {
      mockPrisma.user.findUnique.mockResolvedValue({
        ...mockUser,
        status: 'INACTIVE'
      } as any)

      await expect(digitalSignatureService.signReport(mockSignatureRequest))
        .rejects.toThrow('Signer account is not active')
    })

    it('should reject signing for insufficient role permissions', async () => {
      const invalidRequest = {
        ...mockSignatureRequest,
        signerRole: 'principal' as const
      }

      mockPrisma.user.findUnique.mockResolvedValue({
        ...mockUser,
        role: 'TEACHER' // Teacher cannot sign as principal
      } as any)

      await expect(digitalSignatureService.signReport(invalidRequest))
        .rejects.toThrow('User does not have permission to sign as principal')
    })
  })

  describe('validateSignature', () => {
    const mockSignature = {
      id: 'signature-123',
      reportId: 'report-123',
      signerId: 'user-123',
      signerName: 'Maria Santos',
      signerRole: 'teacher',
      signature: 'mock-signature',
      certificate: 'mock-certificate',
      algorithm: 'RSA-SHA256',
      timestamp: new Date('2024-01-15T10:00:00Z'),
      isValid: true,
      revokedAt: null,
      revokedReason: null,
      report: {
        id: 'report-123',
        name: 'Test Report'
      },
      signer: {
        id: 'user-123',
        firstName: 'Maria',
        lastName: 'Santos',
        email: '<EMAIL>',
        role: 'TEACHER',
        status: 'ACTIVE'
      }
    }

    it('should validate correct signature', async () => {
      mockPrisma.digitalSignature.findUnique.mockResolvedValue(mockSignature as any)

      const result = await digitalSignatureService.validateSignature('signature-123')

      expect(result.isValid).toBe(true)
      expect(result.signatureValid).toBe(true)
      expect(result.certificateValid).toBe(true)
      expect(result.timestampValid).toBe(true)
      expect(result.documentIntegrity).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should detect non-existent signature', async () => {
      mockPrisma.digitalSignature.findUnique.mockResolvedValue(null)

      const result = await digitalSignatureService.validateSignature('non-existent')

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Signature not found')
    })

    it('should detect revoked signature', async () => {
      mockPrisma.digitalSignature.findUnique.mockResolvedValue({
        ...mockSignature,
        revokedAt: new Date(),
        revokedReason: 'Security breach'
      } as any)

      const result = await digitalSignatureService.validateSignature('signature-123')

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Signature was revoked: Security breach')
    })

    it('should warn about inactive signer', async () => {
      mockPrisma.digitalSignature.findUnique.mockResolvedValue({
        ...mockSignature,
        signer: {
          ...mockSignature.signer,
          status: 'INACTIVE'
        }
      } as any)

      const result = await digitalSignatureService.validateSignature('signature-123')

      expect(result.warnings).toContain('Signer account is no longer active')
    })
  })

  describe('revokeSignature', () => {
    it('should revoke signature successfully', async () => {
      const mockSignature = {
        id: 'signature-123',
        reportId: 'report-123',
        signerName: 'Maria Santos',
        timestamp: new Date()
      }

      mockPrisma.digitalSignature.update.mockResolvedValue({} as any)
      mockPrisma.digitalSignature.findUnique.mockResolvedValue(mockSignature as any)
      mockPrisma.generatedReport.update.mockResolvedValue({} as any)

      await digitalSignatureService.revokeSignature(
        'signature-123',
        'Security breach',
        'admin-123'
      )

      expect(mockPrisma.digitalSignature.update).toHaveBeenCalledWith({
        where: { id: 'signature-123' },
        data: {
          isValid: false,
          revokedAt: expect.any(Date),
          revokedReason: 'Security breach'
        }
      })

      expect(mockPrisma.generatedReport.update).toHaveBeenCalledWith({
        where: { id: 'report-123' },
        data: {
          digitalSignature: expect.stringContaining('"isValid":false')
        }
      })
    })
  })

  describe('getSignatureRequirements', () => {
    it('should return correct requirements for SF2', () => {
      const requirements = digitalSignatureService.getSignatureRequirements('SF2')

      expect(requirements.required).toBe(true)
      expect(requirements.signers).toHaveLength(2)
      expect(requirements.signers[0]).toMatchObject({
        role: 'teacher',
        required: true,
        order: 1
      })
      expect(requirements.signers[1]).toMatchObject({
        role: 'principal',
        required: true,
        order: 2
      })
    })

    it('should return correct requirements for SF4', () => {
      const requirements = digitalSignatureService.getSignatureRequirements('SF4')

      expect(requirements.required).toBe(true)
      expect(requirements.signers).toHaveLength(2)
      expect(requirements.signers[0]).toMatchObject({
        role: 'registrar',
        required: true,
        order: 1
      })
      expect(requirements.signers[1]).toMatchObject({
        role: 'principal',
        required: true,
        order: 2
      })
    })

    it('should return no requirements for custom reports', () => {
      const requirements = digitalSignatureService.getSignatureRequirements('custom_attendance')

      expect(requirements.required).toBe(false)
      expect(requirements.signers).toHaveLength(0)
    })
  })
})
