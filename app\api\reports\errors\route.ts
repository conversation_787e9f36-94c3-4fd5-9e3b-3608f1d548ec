import { NextRequest, NextResponse } from 'next/server'
import { getUserFromToken } from '@/lib/auth'
import { ReportError<PERSON><PERSON><PERSON>, ErrorCategory, ErrorSeverity } from '@/lib/services/error-handler'
import { AuditLoggerService, AuditAction } from '@/lib/services/audit-logger'
import { z } from 'zod'

// Validation schema for error resolution
const errorResolutionSchema = z.object({
  errorId: z.string().min(1, 'Error ID is required'),
  resolution: z.string().min(1, 'Resolution description is required')
})

/**
 * GET /api/reports/errors - Get error logs and statistics
 */
export async function GET(request: NextRequest) {
  try {
    // Authentication
    const user = await getUserFromToken(request)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check admin permissions
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'list'
    const category = searchParams.get('category') as ErrorCategory | undefined
    const severity = searchParams.get('severity') as ErrorSeverity | undefined
    const resolved = searchParams.get('resolved') === 'true'
    const userId = searchParams.get('userId') || undefined
    const dateFrom = searchParams.get('dateFrom') ? new Date(searchParams.get('dateFrom')!) : undefined
    const dateTo = searchParams.get('dateTo') ? new Date(searchParams.get('dateTo')!) : undefined
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    const timeframe = searchParams.get('timeframe') as 'hour' | 'day' | 'week' | 'month' || 'day'

    const errorHandler = ReportErrorHandler.getInstance()
    const auditLogger = AuditLoggerService.getInstance()

    switch (action) {
      case 'list':
        const { errors, total } = await errorHandler.listErrors({
          category,
          severity,
          resolved,
          userId,
          dateFrom,
          dateTo,
          limit,
          offset
        })

        // Log access
        await auditLogger.logSuccess(
          user.id,
          AuditAction.ERROR_OCCURRED,
          'error_log',
          {
            details: { action: 'list_errors', filters: { category, severity, resolved } }
          }
        )

        return NextResponse.json({
          success: true,
          errors: errors.map(error => ({
            id: error.id,
            code: error.code,
            message: error.message,
            category: error.category,
            severity: error.severity,
            resolved: error.resolved,
            resolvedAt: error.resolvedAt,
            resolvedBy: error.resolvedBy,
            resolution: error.resolution,
            occurrenceCount: error.occurrenceCount,
            firstOccurrence: error.firstOccurrence,
            lastOccurrence: error.lastOccurrence,
            context: {
              userId: error.context.userId,
              operation: error.context.operation,
              timestamp: error.context.timestamp
            }
          })),
          pagination: {
            total,
            limit,
            offset,
            hasMore: offset + limit < total
          }
        })

      case 'statistics':
        const statistics = await errorHandler.getErrorStatistics(timeframe)

        return NextResponse.json({
          success: true,
          statistics: {
            totalErrors: statistics.totalErrors,
            errorsByCategory: statistics.errorsByCategory,
            errorsBySeverity: statistics.errorsBySeverity,
            topErrors: statistics.topErrors,
            errorTrend: statistics.errorTrend,
            resolutionRate: statistics.resolutionRate
          },
          timeframe
        })

      case 'summary':
        // Get quick summary for dashboard
        const recentErrors = await errorHandler.listErrors({
          dateFrom: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          limit: 10
        })

        const criticalErrors = await errorHandler.listErrors({
          severity: ErrorSeverity.CRITICAL,
          resolved: false,
          limit: 5
        })

        return NextResponse.json({
          success: true,
          summary: {
            recentErrorsCount: recentErrors.total,
            criticalErrorsCount: criticalErrors.total,
            recentErrors: recentErrors.errors.slice(0, 5),
            criticalErrors: criticalErrors.errors
          }
        })

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Error logs API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/reports/errors - Resolve errors or perform error management actions
 */
export async function POST(request: NextRequest) {
  try {
    // Authentication
    const user = await getUserFromToken(request)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check admin permissions
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Parse request body
    const body = await request.json()
    const action = body.action

    const errorHandler = ReportErrorHandler.getInstance()
    const auditLogger = AuditLoggerService.getInstance()

    switch (action) {
      case 'resolve':
        const { errorId, resolution } = errorResolutionSchema.parse(body)

        const success = await errorHandler.resolveError(errorId, resolution, user.id)

        if (!success) {
          return NextResponse.json(
            { success: false, error: 'Failed to resolve error' },
            { status: 404 }
          )
        }

        // Log resolution
        await auditLogger.logSuccess(
          user.id,
          AuditAction.ERROR_RESOLVED,
          'error_log',
          {
            resourceId: errorId,
            details: { resolution }
          }
        )

        return NextResponse.json({
          success: true,
          message: 'Error resolved successfully'
        })

      case 'bulk_resolve':
        const { errorIds, bulkResolution } = body

        if (!Array.isArray(errorIds) || !bulkResolution) {
          return NextResponse.json(
            { success: false, error: 'Error IDs array and resolution are required' },
            { status: 400 }
          )
        }

        let resolvedCount = 0
        for (const id of errorIds) {
          const resolved = await errorHandler.resolveError(id, bulkResolution, user.id)
          if (resolved) resolvedCount++
        }

        // Log bulk resolution
        await auditLogger.logSuccess(
          user.id,
          AuditAction.ERROR_RESOLVED,
          'error_log',
          {
            details: { 
              action: 'bulk_resolve',
              errorIds,
              resolution: bulkResolution,
              resolvedCount
            }
          }
        )

        return NextResponse.json({
          success: true,
          message: `Resolved ${resolvedCount} of ${errorIds.length} errors`,
          resolvedCount,
          totalCount: errorIds.length
        })

      case 'test_error':
        // Test endpoint to generate sample errors for testing
        if (process.env.NODE_ENV !== 'development') {
          return NextResponse.json(
            { success: false, error: 'Test errors only available in development' },
            { status: 403 }
          )
        }

        const testCategory = body.category || ErrorCategory.UNKNOWN
        const testSeverity = body.severity || ErrorSeverity.MEDIUM
        const testMessage = body.message || 'Test error for development'

        const testErrorId = await errorHandler.handleError(
          new Error(testMessage),
          testCategory,
          testSeverity,
          {
            userId: user.id,
            operation: 'test_error_generation',
            additionalData: { generatedBy: 'admin_test' }
          }
        )

        return NextResponse.json({
          success: true,
          message: 'Test error generated',
          errorId: testErrorId
        })

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Error management action failed:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation error',
          details: error.errors
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/reports/errors/[id] - Get specific error details
 */
export async function GET_ERROR_DETAILS(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Authentication
    const user = await getUserFromToken(request)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check admin permissions
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const errorId = params.id
    const errorHandler = ReportErrorHandler.getInstance()

    const error = await errorHandler.getError(errorId)

    if (!error) {
      return NextResponse.json(
        { success: false, error: 'Error not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      error: {
        id: error.id,
        code: error.code,
        message: error.message,
        category: error.category,
        severity: error.severity,
        context: error.context,
        resolved: error.resolved,
        resolvedAt: error.resolvedAt,
        resolvedBy: error.resolvedBy,
        resolution: error.resolution,
        occurrenceCount: error.occurrenceCount,
        firstOccurrence: error.firstOccurrence,
        lastOccurrence: error.lastOccurrence
      }
    })

  } catch (error) {
    console.error('Error details fetch failed:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}
