/**
 * Global Test Teardown
 * 
 * This file runs once after all tests to clean up the testing environment
 */

import fs from 'fs'
import path from 'path'

export default async function globalTeardown() {
  console.log('🧹 Cleaning up test environment...')

  try {
    // Clean up test storage directory
    const testStorageDir = path.join(process.cwd(), 'test-storage')
    if (fs.existsSync(testStorageDir)) {
      fs.rmSync(testStorageDir, { recursive: true, force: true })
      console.log('✓ Cleaned up test storage directory')
    }

    // Clean up test configuration directory
    const testConfigDir = path.join(process.cwd(), 'test-config')
    if (fs.existsSync(testConfigDir)) {
      fs.rmSync(testConfigDir, { recursive: true, force: true })
      console.log('✓ Cleaned up test configuration directory')
    }

    // Clean up any temporary test files
    const tempFiles = [
      'test-report.pdf',
      'test-report.xlsx',
      'test-template.json'
    ]

    for (const file of tempFiles) {
      const filePath = path.join(process.cwd(), file)
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath)
        console.log(`✓ Cleaned up temporary file: ${file}`)
      }
    }

    // Clean up test database (if using a separate test database)
    if (process.env.DATABASE_URL?.includes('test')) {
      try {
        console.log('🗄️  Cleaning up test database...')
        
        // Note: In a real implementation, you might want to:
        // 1. Drop test database tables
        // 2. Reset sequences
        // 3. Clear test data
        
        console.log('✓ Test database cleanup complete')
      } catch (error) {
        console.warn('⚠️  Database cleanup failed (this may be expected in some environments)')
        console.warn(error)
      }
    }

    // Clear global test variables
    delete global.__TEST_STORAGE_DIR__
    delete global.__TEST_CONFIG_DIR__
    delete global.__TEST_MOCK_DATA_DIR__

    console.log('✅ Test environment cleanup complete')

  } catch (error) {
    console.error('❌ Test environment cleanup failed:', error)
    // Don't throw error in teardown to avoid masking test failures
  }
}
