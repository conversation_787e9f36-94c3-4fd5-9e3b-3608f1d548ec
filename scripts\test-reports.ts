#!/usr/bin/env tsx

/**
 * QR-SAMS Reports System Test Runner
 * 
 * This script provides comprehensive testing capabilities for the reports system
 */

import { execSync } from 'child_process'
import fs from 'fs'
import path from 'path'

interface TestOptions {
  coverage: boolean
  watch: boolean
  verbose: boolean
  pattern?: string
  updateSnapshots: boolean
  bail: boolean
  maxWorkers?: number
  silent: boolean
  ci: boolean
}

class ReportsTestRunner {
  private options: TestOptions

  constructor(options: Partial<TestOptions> = {}) {
    this.options = {
      coverage: true,
      watch: false,
      verbose: true,
      updateSnapshots: false,
      bail: false,
      silent: false,
      ci: false,
      ...options
    }
  }

  async run(): Promise<void> {
    console.log('🧪 Starting QR-SAMS Reports System Tests...\n')

    try {
      // Pre-test validation
      await this.validateEnvironment()

      // Run different test suites
      if (this.options.pattern) {
        await this.runSpecificTests()
      } else {
        await this.runAllTests()
      }

      // Post-test reporting
      await this.generateReports()

      console.log('\n✅ All tests completed successfully!')

    } catch (error) {
      console.error('\n❌ Tests failed:', error)
      process.exit(1)
    }
  }

  private async validateEnvironment(): Promise<void> {
    console.log('🔍 Validating test environment...')

    // Check if Jest is installed
    try {
      execSync('npx jest --version', { stdio: 'pipe' })
      console.log('  ✓ Jest is available')
    } catch (error) {
      throw new Error('Jest is not installed. Run: npm install --save-dev jest @types/jest')
    }

    // Check if TypeScript is configured
    const tsConfigPath = path.join(process.cwd(), 'tsconfig.json')
    if (!fs.existsSync(tsConfigPath)) {
      console.warn('  ⚠️  TypeScript configuration not found')
    } else {
      console.log('  ✓ TypeScript configuration found')
    }

    // Check if test files exist
    const testDir = path.join(process.cwd(), '__tests__')
    if (!fs.existsSync(testDir)) {
      throw new Error('Test directory not found. Please create __tests__ directory with test files.')
    }

    const testFiles = this.findTestFiles(testDir)
    console.log(`  ✓ Found ${testFiles.length} test files`)

    // Validate test database configuration
    if (process.env.DATABASE_URL && !process.env.DATABASE_URL.includes('test')) {
      console.warn('  ⚠️  Warning: DATABASE_URL does not appear to be a test database')
    }

    console.log('✅ Environment validation completed\n')
  }

  private findTestFiles(dir: string): string[] {
    const testFiles: string[] = []
    const files = fs.readdirSync(dir)

    for (const file of files) {
      const filePath = path.join(dir, file)
      const stat = fs.statSync(filePath)

      if (stat.isDirectory()) {
        testFiles.push(...this.findTestFiles(filePath))
      } else if (file.endsWith('.test.ts') || file.endsWith('.test.js')) {
        testFiles.push(filePath)
      }
    }

    return testFiles
  }

  private async runAllTests(): Promise<void> {
    console.log('🏃 Running all test suites...\n')

    // Unit tests
    await this.runTestSuite('Unit Tests', '__tests__/services/**/*.test.ts')

    // Integration tests
    await this.runTestSuite('Integration Tests', '__tests__/api/**/*.test.ts')

    // End-to-end tests (if they exist)
    const e2eTestsExist = fs.existsSync(path.join(process.cwd(), '__tests__/e2e'))
    if (e2eTestsExist) {
      await this.runTestSuite('E2E Tests', '__tests__/e2e/**/*.test.ts')
    }
  }

  private async runSpecificTests(): Promise<void> {
    console.log(`🎯 Running specific tests: ${this.options.pattern}\n`)
    await this.runTestSuite('Specific Tests', this.options.pattern!)
  }

  private async runTestSuite(suiteName: string, pattern: string): Promise<void> {
    console.log(`📋 ${suiteName}`)
    console.log('─'.repeat(50))

    const jestArgs = this.buildJestArgs(pattern)
    const command = `npx jest ${jestArgs.join(' ')}`

    try {
      console.log(`Running: ${command}\n`)
      execSync(command, { 
        stdio: this.options.silent ? 'pipe' : 'inherit',
        env: { ...process.env, NODE_ENV: 'test' }
      })
      console.log(`✅ ${suiteName} passed\n`)
    } catch (error) {
      console.error(`❌ ${suiteName} failed\n`)
      if (this.options.bail) {
        throw error
      }
    }
  }

  private buildJestArgs(pattern: string): string[] {
    const args = [pattern]

    if (this.options.coverage) {
      args.push('--coverage')
    }

    if (this.options.watch) {
      args.push('--watch')
    }

    if (this.options.verbose) {
      args.push('--verbose')
    }

    if (this.options.updateSnapshots) {
      args.push('--updateSnapshot')
    }

    if (this.options.bail) {
      args.push('--bail')
    }

    if (this.options.maxWorkers) {
      args.push(`--maxWorkers=${this.options.maxWorkers}`)
    }

    if (this.options.ci) {
      args.push('--ci', '--watchman=false')
    }

    // Always run in band for more predictable results
    args.push('--runInBand')

    return args
  }

  private async generateReports(): Promise<void> {
    console.log('📊 Generating test reports...')

    const reportsDir = path.join(process.cwd(), 'test-reports')
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true })
    }

    // Generate coverage report summary
    const coverageDir = path.join(process.cwd(), 'coverage')
    if (fs.existsSync(coverageDir)) {
      const lcovPath = path.join(coverageDir, 'lcov.info')
      if (fs.existsSync(lcovPath)) {
        console.log('  ✓ Coverage report generated')
        console.log(`  📁 Coverage reports available in: ${coverageDir}`)
      }
    }

    // Generate test summary
    const testSummary = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'test',
      options: this.options,
      testFiles: this.findTestFiles(path.join(process.cwd(), '__tests__')).length
    }

    fs.writeFileSync(
      path.join(reportsDir, 'test-summary.json'),
      JSON.stringify(testSummary, null, 2)
    )

    console.log('  ✓ Test summary generated')
    console.log(`  📁 Test reports available in: ${reportsDir}`)
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2)
  const options: Partial<TestOptions> = {}

  // Parse command line arguments
  for (let i = 0; i < args.length; i++) {
    const arg = args[i]
    
    switch (arg) {
      case '--no-coverage':
        options.coverage = false
        break
      case '--watch':
        options.watch = true
        break
      case '--silent':
        options.silent = true
        options.verbose = false
        break
      case '--update-snapshots':
        options.updateSnapshots = true
        break
      case '--bail':
        options.bail = true
        break
      case '--ci':
        options.ci = true
        break
      case '--pattern':
        options.pattern = args[++i]
        break
      case '--max-workers':
        options.maxWorkers = parseInt(args[++i])
        break
      case '--help':
        console.log(`
QR-SAMS Reports System Test Runner

Usage: tsx scripts/test-reports.ts [options]

Options:
  --no-coverage       Skip coverage collection
  --watch             Run tests in watch mode
  --silent            Run tests silently
  --update-snapshots  Update test snapshots
  --bail              Stop on first test failure
  --ci                Run in CI mode
  --pattern <pattern> Run specific test pattern
  --max-workers <n>   Maximum number of worker processes
  --help              Show this help message

Examples:
  tsx scripts/test-reports.ts                           # Run all tests
  tsx scripts/test-reports.ts --pattern "report-service" # Run specific tests
  tsx scripts/test-reports.ts --watch                   # Run in watch mode
  tsx scripts/test-reports.ts --ci --no-coverage        # CI mode without coverage
`)
        process.exit(0)
    }
  }

  const testRunner = new ReportsTestRunner(options)
  await testRunner.run()
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error)
}

export { ReportsTestRunner }
